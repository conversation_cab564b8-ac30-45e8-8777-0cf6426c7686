import datetime
from enum import Enum
from typing import Any, Optional, Annotated
from sqlalchemy import (
    String, Boolean, DateTime, Numeric, Date,
    ForeignKey, Index, select, UniqueConstraint, ForeignKeyConstraint, func
)
from sqlalchemy.dialects.postgresql import JSONB, ARRAY, TEXT, BIGINT, INTEGER, TSVECTOR, TIMESTAMP
from sqlalchemy.ext.indexable import index_property
from sqlalchemy_utils import ChoiceType, TSVectorType
from sqlalchemy_json import mutable_json_type
from sqlalchemy.orm import  mapped_column, Mapped

from .base import Base
from .user import User


int_pk = Annotated[int, mapped_column(primary_key=True, autoincrement=False)]
timestamp = Annotated[
    datetime.datetime,
    mapped_column(nullable=False, server_default=func.CURRENT_TIMESTAMP()),
]

required_text = Annotated[str, mapped_column(TEXT, nullable=False)]


StatusCategory = [
    ('To Do', 'To Do'),
    ('In Progress', 'In Progress'),
    ('Done', 'Done')
]

priority_severity = [
    ("Show Stopper", "Show Stopper"),
    ("Critical", "Critical"),
    ("High", "High"),
    ("Urgent", "Urgent"),
    ("Medium", "Medium"),
    ("Low", "Low"),
]


class Issue(Base):
    use_snake_case = True
    # Drop column list -
    # issue_hierarchy_level
    id = mapped_column(
        BIGINT, nullable=False, primary_key=True,
        # unique=True
    )
    key = mapped_column(TEXT, nullable=False, index=True)
    parent_id = mapped_column(
        BIGINT,
        # ForeignKey(column="issue.id", deferrable=True, initially='DEFERRED'),
        nullable=True, index=True,
    )
    parent_key = mapped_column(
        String,
        # ForeignKey(column="issue.key", deferrable=True, initially='DEFERRED'),
        nullable=True, index=True,
    )
    summary = mapped_column(String, nullable=False)
    # Source: https://amercader.net/blog/beware-of-json-fields-in-sqlalchemy/
    description = mapped_column(mutable_json_type(dbtype=JSONB, nested=True))
    description_markdown = mapped_column(TEXT)
    # Interesting implementation of custom types like tsvector
    # https://stackoverflow.com/questions/13837111/tsvector-in-sqlalchemy
    tscv_summary_description = mapped_column(TSVectorType, nullable=False)
    isSubTask = mapped_column(Boolean, nullable=False)
    issuetype = mapped_column(String, nullable=False, index=True)
    issue_hierarchy_level = mapped_column(INTEGER, nullable=True)
    status = mapped_column(String, nullable=False, index=True)
    statusCategory = mapped_column(ChoiceType(StatusCategory), nullable=False, index=True)
    statuscategorychangedate = mapped_column(DateTime(timezone=True), nullable=False, )
    resolution = mapped_column(String)
    resolutiondate = mapped_column(DateTime)
    priority = mapped_column(ChoiceType(priority_severity), nullable=True)
    urgency = mapped_column(ChoiceType(priority_severity), nullable=True)
    components = mapped_column(ARRAY(String), nullable=True)
    fixVersions = mapped_column(ARRAY(TEXT), nullable=True)
    versions = mapped_column(ARRAY(TEXT), nullable=True)
    assignee = mapped_column(String, ForeignKey(User.accountId), nullable=True)
    reporter = mapped_column(String, ForeignKey(User.accountId), nullable=False)
    created = mapped_column(DateTime(timezone=True), nullable=False)
    updated = mapped_column(DateTime(timezone=True), nullable=False)
    Rank = mapped_column(String, nullable=False)
    sprintid = mapped_column(ARRAY(BIGINT), nullable=True)
    sprint = mapped_column(ARRAY(String), nullable=True)
    Team = mapped_column(String, nullable=True)
    ClientJira = mapped_column(ARRAY(String), nullable=True)
    startdate = mapped_column(Date, nullable=True)
    duedate = mapped_column(Date, nullable=True)
    originalestimate = mapped_column(Numeric)
    timeoriginalestimate = mapped_column(Numeric)
    timespent = mapped_column(Numeric)
    aggregatetimeoriginalestimate = mapped_column(Numeric)
    aggregatetimeestimate = mapped_column(Numeric)
    aggregatetimespent = mapped_column(Numeric)
    timeestimate = mapped_column(Numeric)
    progress_progress = mapped_column(Numeric)
    progress_total = mapped_column(Numeric)
    progress_percent = mapped_column(Numeric)
    aggregateprogress_progress = mapped_column(Numeric)
    aggregateprogress_total = mapped_column(Numeric)
    aggregateprogress_percent = mapped_column(Numeric)
    totaleffort = mapped_column(Numeric)
    totaldeveffort = mapped_column(Numeric)
    baeffort = mapped_column(Numeric)
    adeffort = mapped_column(Numeric)
    rdeffort = mapped_column(Numeric)
    qaeffort = mapped_column(Numeric)
    contingency = mapped_column(Numeric)
    storypoints = mapped_column(BIGINT, nullable=True)
    testcaseno = mapped_column(String)
    testcasesuite = mapped_column(String)
    teststepno = mapped_column(String)
    scenariono = mapped_column(String)
    reqfinalized = mapped_column(String)
    approvalstatus = mapped_column(String)
    reopen_count = mapped_column(BIGINT)
    # drop these four columns path, path_id, parent_path, parent_path_id
    # path = mapped_column(LtreeType, nullable=True)
    # path_id = mapped_column(LtreeType, nullable=True)
    # parent_path = mapped_column(LtreeType, nullable=True)
    # parent_path_id = mapped_column(LtreeType, nullable=True)
    qc_check = mapped_column(TEXT, nullable=True)
    cvss_score = mapped_column(Numeric, nullable=True)
    initiated_by = mapped_column(TEXT, nullable=True)
    change_risk = mapped_column(TEXT, nullable=True)
    category_type = mapped_column(TEXT, nullable=True)
    severity = mapped_column(TEXT, nullable=True)
    initiative_detail = mapped_column(TEXT, nullable=True, index=True)

    __table_args__ = (
        Index(
            "idx_issue_fixVersions", fixVersions, postgresql_using='gin'
        ),
        Index(
            "idx_issue_versions", versions, postgresql_using='gin'
        ),
        Index(
            "idx_issue_tsvector_summary_description", tscv_summary_description,
            postgresql_using='gin'
        ),
        UniqueConstraint("id", name="uq_issue_id"),
        UniqueConstraint("key", name="uq_issue_key"),
        ForeignKeyConstraint([parent_id], [id], name="fk_issue_parent_id"),
        ForeignKeyConstraint([parent_key], [key], name="fk_issue_parent_key"),
    )

    # assignee_name = mapped_column_property(select(User.displayName).where(User.accountId == assignee).scalar_subquery())
    # reporter_name = mapped_column_property(select(User.displayName).where(User.accountId == reporter).scalar_subquery())

    # team_name = column_property(
    #     select(Teams.team_name).where(Teams.accountId == assignee)
    #     .filter(created.between(Teams.startDate, Teams.endDate))
    #     .scalar_subquery()
    # )
    # I don't want an explicit relationship on key and parent mapped_columns
    # children = relationship("Issue", back_populates="parent", remote_side=[parent_id], foreign_keys=[parent_id])
    # The below commented one also works.
    # parent = relationship("Issue", back_populates="children", remote_side=[id], primaryjoin="Issue.id == Issue.parent_id")
    # parent = relationship("Issue", back_populates="children", primaryjoin=remote(id) == foreign(parent_id))

    # assignee_user = relationship(
    #     "User", back_populates="assigned_issues", foreign_keys=[assignee]
    # )
    # reporter_user = relationship("User", back_populates="reported_issues", foreign_keys=[reporter])
    #
    # worklogs = relationship("WorkLog", back_populates='issue', primaryjoin="Issue.key == WorkLog.issue_key")


class IssueComments(Base):
    use_snake_case = True

    id: Mapped[int_pk]
    author: Mapped[str] = mapped_column(TEXT, ForeignKey(User.accountId))
    body: Mapped[dict[str, Any]]
    renderedBody: Mapped[str] = mapped_column(TEXT)
    updateAuthor: Mapped[str] = mapped_column(TEXT, ForeignKey(User.accountId))
    created: Mapped[timestamp]
    updated: Mapped[timestamp]
    jsdPublic: Mapped[bool]
    issue_id: Mapped[int] = mapped_column(BIGINT, ForeignKey(Issue.id))
    issue_key: Mapped[str] = mapped_column(ForeignKey(Issue.key))


class IssueLinks(Base):
    use_snake_case = True

    id: Mapped[int_pk]
    type: Mapped[dict[str, Any]]
    outwardIssue_id: Mapped[Optional[int]] = mapped_column(ForeignKey(Issue.id))
    outwardIssue_key: Mapped[Optional[str]] = mapped_column(ForeignKey(Issue.key))
    inwardIssue_id: Mapped[Optional[int]] = mapped_column(ForeignKey(Issue.id))
    inwardIssue_key: Mapped[Optional[str]] = mapped_column(ForeignKey(Issue.key))
    issue_id: Mapped[int] = mapped_column(BIGINT, ForeignKey(Issue.id))
    issue_key: Mapped[str] = mapped_column(ForeignKey(Issue.key))


class IssueFields(Base):
    use_snake_case = True

    id: Mapped[str] = mapped_column(TEXT, primary_key=True, autoincrement=False)
    key: Mapped[required_text]
    name: Mapped[required_text]
    untranslatedName: Mapped[Optional[str]]
    custom: Mapped[bool]
    navigable: Mapped[bool]
    orderable: Mapped[bool]
    searchable: Mapped[bool]
    schema: Mapped[dict[str, Any]]
    clauseNames: Mapped[list[str]] = mapped_column(
        ARRAY(TEXT),
        comment="The names that can be used to reference the field in an advanced search",
        doc="The names that can be used to reference the field in an advanced search"
    )

    # Define index properties for JSON fields using index_property
    schema_type = index_property(attr_name='schema', index='type')
    schema_custom = index_property('schema', 'custom')
    schema_custom_id = index_property('schema', 'customId')
    schema_configuration = index_property('schema', 'configuration')


    __table_args__ = (
        {'schema': 'public'}
    )

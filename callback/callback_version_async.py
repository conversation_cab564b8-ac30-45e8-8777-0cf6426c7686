"""
Modern Async Version Callbacks
Optimized version of _callback_version_.py with async support and improved performance
"""

import os
import sys
import time
import traceback

from typing import List, Dict, Optional, Tuple, NamedTuple, Counter

from dash import callback, Output, Input, State, callback_context, html
from dash.exceptions import PreventUpdate
from dependency_injector.wiring import inject, Provide

from container import DatabaseContainer
from data import MyLogger, get_from_db as db, clicked
from data.helper import (
    generate_layer_bullet, apply_href, apply_td, apply_acronymn,
    apply_dbc_progress, apply_list_to_string, num_to_words
)

logger = MyLogger().get_logger()

# Data structures for better performance
class ReleaseMetrics(NamedTuple):
    story_todo: int
    story_wip: int
    story_done: int
    defect_todo: int
    defect_wip: int
    defect_done: int
    other_todo: int
    other_wip: int
    other_done: int
    subtask_todo: int
    subtask_wip: int
    subtask_done: int


class DefectRow(NamedTuple):
    key: str
    summary: str
    status: str
    statusclass: str
    priority: str
    severity: str
    assignee: str
    aging: int
    team: str


@callback(
    Output("id-version-search", "value"),
    Output("id-versions", "value"),
    Input("id-version-serach-button", "n_clicks"),
    Input("id-version-search", "n_submit"),
    State("id-version-search", "value"),
    State("url", "pathname"),
    prevent_initial_call=True
)
@inject
def get_versions_by_pattern(
        search_click: int,
        n_submit: int,
        pattern: str,
        pathname: str,
        session_factory=Provide[DatabaseContainer],
) -> Tuple[str, List[str]]:
    """
    Get versions by pattern with improved performance
    """
    if not pattern or pattern.strip() == '':
        raise PreventUpdate

    try:
        schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
        version_filter_list = [p.strip() for p in pattern.split(",")]

        session_factory.config.postgres_connection_key.override(f"{schema_name}_ro")

        SessionLocal = session_factory.postgres_sync_sessions()
        with SessionLocal() as pg_session:
            versions = db.get_all_active_versions_natsort(pg_session)

        # Use sets for faster filtering
        versions_set = set(versions)
        result_add = set()
        result_remove = set()

        for filter_pattern in version_filter_list:
            if filter_pattern.startswith('~'):
                # Exclude pattern
                exclude_pattern = filter_pattern[1:]
                result_remove.update(v for v in versions_set if v.startswith(exclude_pattern))
            else:
                # Include pattern
                result_add.update(v for v in versions_set if v.startswith(filter_pattern))

        # Calculate final result and maintain original order
        pattern_set = result_add - result_remove
        pattern_list = [v for v in versions if v in pattern_set]

        logger.debug(f'Version patterns found: {len(pattern_list)} versions')
        return "", pattern_list

    except Exception as e:
        logger.error(f"Error in get_versions_by_pattern: {e}")
        exc_type, exc_value, exc_tb = sys.exc_info()
        line_num = exc_tb.tb_lineno
        tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
        logger.error(
            f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
            exc_info=True
        )
        raise PreventUpdate


# DISABLED: Server-side layer switching conflicts with client-side modern version
# Using client-side only approach for better performance and consistency
# @callback(
#     [
#         Output("id-version-bullet-1", "className"),
#         Output("id-version-bullet-2", "className"),
#         Output("id-version-bullet-3", "className"),
#         Output("id-version-bullet-5", "className"),
#         Output("id-version-bullet-6", "className"),
#         Output("id-version-bullet-7", "className"),
#         Output("id-version-bullet-8", "className"),
#         Output("id-version-layer-1", "className"),
#         Output("id-version-layer-2", "className"),
#         Output("id-version-layer-3", "className"),
#         Output("id-version-layer-5", "className"),
#         Output("id-version-layer-6", "className"),
#         Output("id-version-layer-7", "className"),
#         Output("id-version-layer-8", "className"),
#         # Output("id-version-main-header", "children"),
#         Output("id-release-type", "className"),
#         Output("id-release-selector", "className"),
#         Output("id-release-status-label", "className"),
#         Output("id-release-status-value", "className"),
#         Output("id-release-priority-label", "className"),
#         Output("id-release-priority-value", "className"),
#         Output("id-release-alert", "className"),
#         Output("id-release-counts", "className"),
#         Output("id-release-urgency", "className"),
#     ],
#     [
#         Input("id-version-bullet-1", "n_clicks"),
#         Input("id-version-bullet-2", "n_clicks"),
#         Input("id-version-bullet-3", "n_clicks"),
#         Input("id-version-bullet-5", "n_clicks"),
#         Input("id-version-bullet-6", "n_clicks"),
#         Input("id-version-bullet-7", "n_clicks"),
#         Input("id-version-bullet-8", "n_clicks"),
#     ]
# )
def toggle_cards_disabled(*args) -> Tuple:
    """
    DISABLED: Toggle cards with optimized logic
    This function is disabled to prevent conflicts with client-side layer switching
    """
    # Disabled - using client-side only approach
    raise PreventUpdate


@callback(
    [
        Output("id-release-type", "className"),
        Output("id-release-selector", "className"),
        Output("id-release-status-label", "className"),
        Output("id-release-status-value", "className"),
        Output("id-release-priority-label", "className"),
        Output("id-release-priority-value", "className"),
        Output("id-release-alert", "className"),
        Output("id-release-counts", "className"),
        Output("id-release-urgency", "className"),
    ],
    [
        Input("id-version-bullet-1", "n_clicks"),
        Input("id-version-bullet-2", "n_clicks"),
        Input("id-version-bullet-3", "n_clicks"),
        Input("id-version-bullet-5", "n_clicks"),
        Input("id-version-bullet-6", "n_clicks"),
        Input("id-version-bullet-7", "n_clicks"),
        Input("id-version-bullet-8", "n_clicks"),
    ]
)
def toggle_sidebar_visibility(*args) -> Tuple:
    """
    Handle sidebar filter visibility based on active layer
    Only manages sidebar elements, not layer switching
    """
    click_event = clicked(callback_context)
    if click_event is None:
        raise PreventUpdate

    # Extract layer number from click event
    layer_number = int(click_event.split('-')[3])
    layer_name = num_to_words[layer_number]

    # Static visibility options for sidebar elements only
    VISIBILITY_CLASSES = {
        "eight": [
            "filter-section", "modern-dropdown", "filter-section", "modern-dropdown",
            "filter-section", "modern-dropdown", "stats-container", "filter-section", "stats-container"
        ],
        "default": ["filter-section hide"] * 3 + ["modern-dropdown hide"] * 3 + ["stats-container hide"] * 3
    }

    visibility_classes = VISIBILITY_CLASSES.get(layer_name, VISIBILITY_CLASSES["default"])
    return tuple(visibility_classes)


@callback(
    Output("id-version-layer-3", "children"),
    Output("id-copy-epic", "content"),
    Input("id-versions", "value"),
    State("url", "pathname"),
    State("id-versions", "options"),
    prevent_initial_call=True
)
@inject
def update_version_details(
        version_no: Optional[List[str]],
        pathname: str,
        version_label: List[Dict],
        session_factory: DatabaseContainer = Provide[DatabaseContainer],
) -> Tuple[html.Div, str]:
    """
    Update version details with improved performance
    """
    if not version_no:
        return create_empty_table(), ""

    try:
        start_time = time.perf_counter()
        project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
        version_lbl = [x['label'] for x in version_label if x['value'] in version_no]

        session_factory.config.postgres_connection_key.override(f"{project}_ro")
        SessionLocal = session_factory.postgres_sync_sessions()

        with SessionLocal() as pg_session:
            query_start = time.perf_counter()
            df = db.get_release_status(version_lbl, pg_session)
            query_time = (time.perf_counter() - query_start) * 1000
            logger.debug(f"Query Time get_release_status: {query_time:.3f} ms")

        if df.empty:
            return create_no_content_div(), ""

        # Process data more efficiently without pandas overhead
        processed_data = process_release_data_optimized(df)
        # Todo Fix this
        # clipboard_epic_list = ", ".join(row.get('epic', '') for row in processed_data)
        final_table = create_release_table(processed_data)
        print(f"final table start")
        print(final_table)
        print(f"final table end")

        total_time = (time.perf_counter() - start_time) * 1000
        logger.info(f'update_version_details: Total time taken: {total_time:.3f} ms')

        # return final_table, clipboard_epic_list
        return final_table, ""

    except Exception as e:
        logger.error(f"Error in update_version_details: {e}")
        exc_type, exc_value, exc_tb = sys.exc_info()
        line_num = exc_tb.tb_lineno
        tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
        logger.error(
            f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
            exc_info=True
        )
        return create_error_div(str(e)), ""


def process_release_data_optimized(df) -> List[Dict]:
    """
    Process release data without heavy pandas operations
    """
    # Convert DataFrame to list of dicts for faster processing
    data = df.to_dict('records')

    # Sort by initiative_id
    data.sort(key=lambda x: x.get('initiative_id', 0))

    processed_data = []

    for row in data:
        # Round numeric columns
        for col in ['estimated_effort', 'actual_effort', 'variance']:
            if col in row and row[col] is not None:
                row[col] = round(row[col], 1)

        # Process list columns
        for col in ['clientjira', 'components']:
            if col in row and row[col]:
                row[col] = apply_list_to_string(row[col])

        # Apply href formatting
        for col in ['initiative', 'epic']:
            if col in row and row[col]:
                row[col] = apply_href(row[col])

        # Create progress bars
        progress_mappings = [
            ('dbc_story', 'story_todo', 'story_wip', 'storydone'),
            ('dbc_other', 'other_todo', 'other_wip', 'otherdone'),
            ('dbc_defect', 'defect_todo', 'defect_wip', 'defectsdone'),
            ('dbc_subtasks', 'subtask_todo', 'subtask_wip', 'subtaskdone')
        ]

        for new_col, todo_col, wip_col, done_col in progress_mappings:
            if all(col in row for col in [todo_col, wip_col, done_col]):
                row[new_col] = apply_dbc_progress(
                    row[todo_col], row[wip_col], row[done_col]
                )

        # Apply table cell formatting
        # This is causing duplicate Td to be applied
        # for key, value in row.items():
        #     if value is not None:
        #         row[key] = apply_td(value)

        # Clean up unnecessary columns
        columns_to_remove = [
            'storydone', 'otherdone', 'defectsdone', 'subtaskdone',
            'story_todo', 'story_wip', 'other_todo', 'other_wip',
            'defect_todo', 'defect_wip', 'subtask_todo', 'subtask_wip',
            'initiative_id'
        ]

        for col in columns_to_remove:
            row.pop(col, None)

        # Rename columns
        rename_map = {
            'dbc_story': 'Story',
            'dbc_other': 'Others',
            'dbc_defect': 'Bugs',
            'dbc_subtasks': 'subtasks'
        }

        for old_key, new_key in rename_map.items():
            if old_key in row:
                row[new_key] = row.pop(old_key)

        processed_data.append(row)

    return processed_data


def create_empty_table() -> html.Div:
    """Create empty state table"""
    return html.Div(
        className="empty-state",
        children=[
            html.Div(
                className="empty-state-content",
                children=[
                    html.I(className="empty-icon", children="📊"),
                    html.H3("No Versions Selected", className="empty-title"),
                    html.P("Select one or more versions to view release status", className="empty-description")
                ]
            )
        ]
    )


def create_no_content_div() -> html.Div:
    """Create no content div"""
    return html.Div(
        className="no-content-modern",
        children=[
            html.I(className="no-content-icon", children="📄"),
            html.H3("No Content Found"),
            html.P("No release data available for the selected versions")
        ]
    )


def create_error_div(error_message: str) -> html.Div:
    """Create error div"""
    return html.Div(
        className="error-state",
        children=[
            html.I(className="error-icon", children="⚠️"),
            html.H3("Error Loading Data"),
            html.P(f"An error occurred while loading release data: {error_message}")
        ]
    )


def create_release_table(data: List[Dict]) -> html.Div:
    """
    Create release status table from processed data with consistent styling
    """
    if not data:
        return create_no_content_div()

    # Get column names from first row
    columns = list(data[0].keys())

    return html.Div(
        className="layer-content table-container",
        children=[
            html.Div(
                className="table-wrapper",
                children=[
                    html.Table(
                        className="modern-table",  # Use consistent table class
                        id="id-version-release-table",  # Add ID for JavaScript targeting
                        children=[
                            html.Thead(
                                children=[
                                    html.Tr(
                                        className="table-header-row",
                                        children=[html.Th(col, className="sortable") for col in columns]
                                    )
                                ]
                            ),
                            html.Tbody(
                                id="id-version-release-table-body",  # Add ID for JavaScript targeting
                                children=[
                                    html.Tr(
                                        className="table-data-row",
                                        children=[html.Td(row.get(col, "")) for col in columns]
                                    ) for row in data
                                ]
                            )
                        ]
                    )
                ]
            )
        ]
    )


@callback(
    [
        Output("id-release-status-value", "options"),
        Output("id-release-status-value", "disabled"),
        Output("id-release-status-value", "value"),
        Output("id-release-priority-value", "disabled"),
        Output("id-release-priority-value", "value"),
    ],
    [
        Input("id-versions", "value"),
        Input("id-release-selector", "value"),
    ],
    [
        State("id-versions", "options"),
        State("url", "pathname"),
    ],
    prevent_initial_call=True
)
@inject
def populate_project_values(
        version_no: Optional[List[str]],
        release_selector: int,
        version_label: List[Dict],
        pathname: str,
        session_factory: DatabaseContainer = Provide[DatabaseContainer],
) -> Tuple[List[Dict], bool, str, bool, str]:
    """
    Populate project values with improved performance
    """
    if not version_no:
        return [], True, "", True, ""

    try:
        schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
        session_factory.config.postgres_connection_key.override(f"{schema_name}_ro")
        version_lbl = [x['label'] for x in version_label if x['value'] in version_no]

        SessionLocal = session_factory.postgres_sync_sessions()
        with SessionLocal() as pg_session:
            # Use synchronous version - create a sync wrapper if needed
            df = db.get_release_defect_status(version_lbl, pg_session, release_selector)

        logger.debug(f"populate_project_values: {len(df)} rows returned")

        if not df.empty:
            # Vectorized filtering and unique extraction
            filtered_statuses = df.loc[~df['statusclass'].isin(['Done', 'Misc']), 'statusclass'].dropna().unique()
            status_options = [{'label': 'ALL', 'value': 'ALL'}] + \
                             [{'label': status, 'value': status} for status in sorted(filtered_statuses)]

            return status_options, False, 'ALL', False, 'ALL'

        return [], True, "", True, ""


    except Exception as e:
        logger.error(f"Error in get_versions_by_pattern: {e}")
        exc_type, exc_value, exc_tb = sys.exc_info()
        line_num = exc_tb.tb_lineno
        tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
        logger.error(
            f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
            exc_info=True
        )
        logger.error(f"Error in populate_project_values: {e}")
        return [], True,  "", True,  ""


@callback(
    [
        Output("id-version-bugs-table-body", "children"),
        Output("id-release-alert", "children"),
        Output("id-release-urgency", "children"),
    ],
    [
        Input("id-versions", "value"),
        Input("id-release-selector", "value"),
        Input("id-release-status-value", "value"),
        Input("id-release-priority-value", "value"),
    ],
    [
        State("id-versions", "options"),
        State("url", "pathname"),
    ],
    prevent_initial_call=True
)
@inject
def show_defect_details(
        version_no: Optional[List[str]],
        release_selector: int,
        custom_status: str,
        team_name: str,
        version_label: List[Dict],
        pathname: str,
        session_factory: DatabaseContainer = Provide[DatabaseContainer],
):
    """
    Show defect details with improved performance
    """
    if not version_no:
        return [create_empty_row("Select versions to view defect details")], \
            html.Span("Select versions to view statistics"), \
            html.Span("Select versions to view statistics")

    try:
        schema_name = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
        version_lbl = [x['label'] for x in version_label if x['value'] in version_no]

        session_factory.config.postgres_connection_key.override(f"{schema_name}_ro")
        SessionLocal = session_factory.postgres_sync_sessions()

        with SessionLocal() as pg_session:
            start_time = time.perf_counter()
            df = db.get_release_defect_status(
                version_lbl, pg_session, int(release_selector)
            )
            query_time = (time.perf_counter() - start_time) * 1000
            logger.debug(f'Query Execution Time get_release_defect_status: {query_time:.3f} ms')

        if df.shape[0] == 0:
            return [create_empty_row("No defect data found for selected criteria")], \
                html.Span("No Defects"), html.Span("No Defects")

        # Convert DataFrame to list of dicts for processing
        defect_data = df.to_dict('records')

        # Filter data efficiently
        filtered_data = apply_defect_filters_optimized(defect_data, custom_status, team_name)

        if not filtered_data:
            return [create_empty_row("No defects match the selected filters")], \
                html.Span("No Matching Defects"), html.Span("No Matching Defects")

        # Process defect data
        table_rows, alert_span, urgency_span = process_defect_data_optimized(
            filtered_data, schema_name, session_factory
        )

        return table_rows, alert_span, urgency_span

    except Exception as e:
        logger.error(f"Error in show_defect_details: {e}")
        exc_type, exc_value, exc_tb = sys.exc_info()
        line_num = exc_tb.tb_lineno
        tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
        logger.error(
            f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
            exc_info=True
        )
        return [create_empty_row(f"Error loading defect data: {str(e)}")], \
            html.Span("Error"), html.Span("Error")


def create_empty_row(message: str) -> html.Tr:
    """Create empty table row with message"""
    return html.Tr(
        className="empty-row",
        children=[html.Td(message, colSpan=18, className="empty-message")]
    )


def apply_defect_filters_optimized(data: List[Dict], custom_status: str, team_name: str) -> List[Dict]:
    """
    Apply filters to defect data efficiently
    """
    filtered_data = []

    for row in data:
        # Apply status filter
        if custom_status and 'ALL' not in custom_status:
            if row.get('statusclass') != custom_status:
                continue

        # Apply priority filter
        priority = row.get('priority', '')
        if team_name == 'FILTER_HIGH':
            if priority not in {'Show Stopper', 'Critical', 'High'}:
                continue
        elif team_name == 'FILTER_OUT_HIGH':
            if priority not in {'Medium', 'Low'}:
                continue

        filtered_data.append(row)

    return filtered_data


def process_defect_data_optimized(
        data: List[Dict],
        schema_name: str,
        session_factory
) -> Tuple[List[html.Tr], html.Span, html.Span]:
    """
    Process defect data efficiently without heavy pandas operations
    """
    # Filter out done issues
    active_defects = [
        row for row in data
        if row.get('statusCategory') != 'Done'
    ]

    # Fill missing severity values
    for row in active_defects:
        if not row.get('severity'):
            row['severity'] = "NA"

    # Sort data
    priority_order = ['Show Stopper', 'Critical', 'Urgent', 'High', 'Medium', 'Low', 'NA']

    def sort_key(row):
        severity = row.get('severity', 'NA')
        priority = row.get('priority', 'NA')
        aging = row.get('aging', 0)

        try:
            severity_idx = priority_order.index(severity)
        except ValueError:
            severity_idx = len(priority_order)

        try:
            priority_idx = priority_order.index(priority)
        except ValueError:
            priority_idx = len(priority_order)

        return (severity_idx, -aging, priority_idx)

    active_defects.sort(key=sort_key)

    # Generate statistics efficiently
    priority_counts = Counter(row.get('priority', 'NA') for row in active_defects)
    severity_counts = Counter(row.get('severity', 'NA') for row in active_defects if row.get('severity') != 'NA')

    # Add test case mapping if in office network
    try:
        import platform
        if platform.uname().node == 'dashboard':
            add_test_case_mapping(active_defects, session_factory)
        else:
            for row in active_defects:
                row['Test_name'] = ''
                row['Automationstatus'] = ''
    except Exception as e:
        logger.warning(f"Could not add test case mapping: {e}")
        logger.error(f"Error in get_versions_by_pattern: {e}")
        exc_type, exc_value, exc_tb = sys.exc_info()
        line_num = exc_tb.tb_lineno
        tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
        logger.error(
            f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
            exc_info=True
        )
        for row in active_defects:
            row['Test_name'] = ''
            row['Automationstatus'] = ''

    # Apply formatting
    for row in active_defects:
        # Apply href to key
        if row.get('key'):
            row['key'] = apply_href(row['key'])

        # Apply table cell formatting to all values
        for key, value in row.items():
            if value is not None:
                row[key] = apply_td(value)

    # Generate alert strings
    alert_string = generate_alert_string(priority_counts, 'priority')
    alert_string_severity = generate_alert_string(severity_counts, 'severity')

    # Generate table rows
    table_rows = []
    for row in active_defects:
        # Get values in consistent order
        row_values = [
            row.get(col, '') for col in [
                'key', 'summary', 'status', 'priority', 'severity',
                'assignee', 'aging', 'team', 'components'
            ]
        ]
        table_rows.append(
            html.Tr(className="defect-row", children=row_values)
        )

    return table_rows, html.Span(alert_string), html.Span(alert_string_severity)


def add_test_case_mapping(defect_data: List[Dict], session_factory) -> None:
    """Add test case mapping for production environment"""
    try:
        session_factory.config_schema.override({'schema_name': 'mssql'})
        with session_factory.db_conn_provider().create_engine() as connection:
            with connection.connect() as conn:
                test_cases = db.get_test_case_mapping(conn)

                # Create lookup dict for faster matching
                test_case_lookup = {
                    str(tc.TC_Number): {
                        'Test_name': tc.Test_name,
                        'Automationstatus': tc.Automationstatus
                    }
                    for tc in test_cases
                }

                # Add test case info to defects
                for row in defect_data:
                    test_case_num = str(row.get('Test Case#', ''))
                    if test_case_num in test_case_lookup:
                        row.update(test_case_lookup[test_case_num])
                    else:
                        row['Test_name'] = ''
                        row['Automationstatus'] = ''

    except Exception as e:
        logger.warning(f"Failed to add test case mapping: {e}")
        logger.error(f"Error in get_versions_by_pattern: {e}")
        exc_type, exc_value, exc_tb = sys.exc_info()
        line_num = exc_tb.tb_lineno
        tb = traceback.TracebackException(exc_type, exc_value, exc_tb)
        logger.error(
            f"Line {line_num} Error encountered: {''.join(tb.format_exception_only())}",
            exc_info=True
        )
        for row in defect_data:
            row['Test_name'] = ''
            row['Automationstatus'] = ''


def generate_alert_string(counts: Counter, field_type: str) -> str:
    """Generate alert string from counts"""
    if not counts:
        return "No Defects"

    # Sort by priority/severity order
    priority_order = ['Show Stopper', 'Critical', 'Urgent', 'High', 'Medium', 'Low', 'NA']

    sorted_items = []
    for item in priority_order:
        if item in counts:
            acronym = apply_acronymn(item)
            sorted_items.append(f"{acronym}: {counts[item]}")

    # Add any items not in priority order
    for item, count in counts.items():
        if item not in priority_order:
            acronym = apply_acronymn(item)
            sorted_items.append(f"{acronym}: {count}")

    return ", ".join(sorted_items) if sorted_items else "No Defects"


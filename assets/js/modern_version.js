/**
 * Modern Version Page JavaScript Functionality
 * Handles tab switching, sidebar toggle, and table interactions
 */

window.modernVersion = {
    // State management
    state: {
        activeLayer: 'three',
        sidebarCollapsed: false,
        isMobile: window.innerWidth <= 1200,
        sortColumn: null,
        sortDirection: 'asc'
    },

    // Initialize the version page functionality
    init: function() {
        this.bindEvents();
        this.handleResize();
        this.initializeTabs();
        this.initializeTable();
        
        console.log('Modern Version Page initialized');
    },

    // Bind event listeners
    bindEvents: function() {
        // Window resize handler
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Tab button clicks
        document.addEventListener('click', (e) => {
            const tabButton = e.target.closest('.tab-button');
            if (tabButton) {
                e.preventDefault();
                const layer = tabButton.getAttribute('data-layer');
                this.switchLayer(layer);
            }
        });

        // Sidebar toggle
        document.addEventListener('click', (e) => {
            const toggleButton = e.target.closest('.sidebar-toggle');
            if (toggleButton) {
                e.preventDefault();
                this.toggleSidebar();
            }
        });

        // Table sorting
        document.addEventListener('click', (e) => {
            const sortableHeader = e.target.closest('.sortable');
            if (sortableHeader) {
                e.preventDefault();
                this.sortTable(sortableHeader);
            }
        });

        // Copy epic button
        document.addEventListener('click', (e) => {
            if (e.target.closest('#id-copy-epic-btn')) {
                this.copyEpicList();
            }
        });
    },

    // Handle window resize
    handleResize: function() {
        const wasMobile = this.state.isMobile;
        this.state.isMobile = window.innerWidth <= 1200;
        
        if (wasMobile !== this.state.isMobile) {
            this.updateLayout();
        }
    },

    // Initialize tab functionality
    initializeTabs: function() {
        // Set default active tab
        const defaultTab = document.querySelector('[data-layer="three"]');
        const defaultLayer = document.getElementById('id-version-layer-3');
        console.log("initializeTabs called")
        
        if (defaultTab && defaultLayer) {
            defaultTab.classList.add('active');
            defaultLayer.classList.add('active');
            this.state.activeLayer = 'three';
        }
    },

    // Initialize table functionality
    initializeTable: function() {
        // Add sorting indicators to table headers (only if they don't already exist)
        const sortableHeaders = document.querySelectorAll('.sortable');
        sortableHeaders.forEach(header => {
            // Check if indicator already exists
            if (!header.querySelector('.sort-indicator')) {
                const indicator = document.createElement('span');
                indicator.className = 'sort-indicator';
                indicator.innerHTML = '↕️';
                indicator.style.marginLeft = '8px';
                indicator.style.fontSize = '12px';
                header.appendChild(indicator);
            }
        });

        console.log(`Initialized sorting for ${sortableHeaders.length} table headers`);
    },

    // Switch between content layers
    switchLayer: function(targetLayer) {
        console.log(`Switching to layer: ${targetLayer}`);

        // Remove active class from all tabs and layers
        document.querySelectorAll('.tab-button').forEach(tab => {
            tab.classList.remove('active');
        });

        document.querySelectorAll('.content-layer').forEach(layer => {
            layer.classList.remove('active');
            // Clear any inline styles that might conflict with CSS
            layer.style.opacity = '';
            layer.style.visibility = '';
            layer.style.position = '';
        });

        // Add active class to target tab and layer
        const targetButton = document.querySelector(`[data-layer="${targetLayer}"]`);
        const targetLayerElement = document.getElementById(`id-version-layer-${targetLayer}`);

        if (targetButton && targetLayerElement) {
            console.log(`Activating - Target button: ${targetButton.id}, Target layer: ${targetLayerElement.id}`);
            targetButton.classList.add('active');
            targetLayerElement.classList.add('active');

            this.state.activeLayer = targetLayer;

            // Animate layer switch
            this.animateLayerSwitch(targetLayerElement);

            // Update layer title based on selection
            this.updateLayerTitle(targetLayer);

            // Re-initialize table functionality for the active layer
            setTimeout(() => {
                this.initializeTable();
            }, 100);

            console.log(`Layer switch complete. Active layer: ${targetLayer}`);
        } else {
            console.error(`Could not find target button or layer for: ${targetLayer}`);
        }
    },

    // Animate layer switch
    animateLayerSwitch: function(layer) {
        layer.style.opacity = '0';
        layer.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            layer.style.transition = 'all 0.3s ease-out';
            layer.style.opacity = '1';
            layer.style.transform = 'translateY(0)';
        }, 50);
    },

    // Update layer title based on active layer
    updateLayerTitle: function(layer) {
        const titleMap = {
            'one': 'Unresolved Issue Count',
            'two': 'Related Issue Count',
            'three': 'Release Status',
            'five': 'Release Roadmap',
            'six': 'Issue Type Analysis',
            'seven': 'Release Roadmap',
            'eight': 'Defects Dashboard'
        };
        
        const headerElement = document.getElementById('id-version-main-header');
        if (headerElement && titleMap[layer]) {
            headerElement.textContent = titleMap[layer];
        }
    },

    // Toggle sidebar visibility
    toggleSidebar: function() {
        const sidebar = document.querySelector('.modern-sidebar');
        if (!sidebar) return;
        
        this.state.sidebarCollapsed = !this.state.sidebarCollapsed;
        
        if (this.state.sidebarCollapsed) {
            sidebar.style.transform = 'translateX(-100%)';
            sidebar.style.position = 'absolute';
        } else {
            sidebar.style.transform = 'translateX(0)';
            sidebar.style.position = 'sticky';
        }
    },

    // Sort table by column
    sortTable: function(header) {
        const table = header.closest('table');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr:not(.empty-row)'));
        
        if (rows.length === 0) return;
        
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const currentColumn = header.textContent.trim();
        
        // Determine sort direction
        let direction = 'asc';
        if (this.state.sortColumn === currentColumn) {
            direction = this.state.sortDirection === 'asc' ? 'desc' : 'asc';
        }
        
        this.state.sortColumn = currentColumn;
        this.state.sortDirection = direction;
        
        // Sort rows
        rows.sort((a, b) => {
            const aValue = a.children[columnIndex]?.textContent.trim() || '';
            const bValue = b.children[columnIndex]?.textContent.trim() || '';
            
            // Try to parse as numbers first
            const aNum = parseFloat(aValue);
            const bNum = parseFloat(bValue);
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return direction === 'asc' ? aNum - bNum : bNum - aNum;
            }
            
            // String comparison
            return direction === 'asc' 
                ? aValue.localeCompare(bValue)
                : bValue.localeCompare(aValue);
        });
        
        // Update sort indicators
        document.querySelectorAll('.sort-indicator').forEach(indicator => {
            indicator.innerHTML = '↕️';
        });
        
        const currentIndicator = header.querySelector('.sort-indicator');
        if (currentIndicator) {
            currentIndicator.innerHTML = direction === 'asc' ? '↑' : '↓';
        }
        
        // Re-append sorted rows
        rows.forEach(row => tbody.appendChild(row));
    },

    // Copy epic list to clipboard
    copyEpicList: function() {
        const clipboardElement = document.getElementById('id-copy-epic');
        if (clipboardElement && clipboardElement.content) {
            // Trigger the clipboard copy
            if (navigator.clipboard) {
                navigator.clipboard.writeText(clipboardElement.content).then(() => {
                    this.showNotification('Epic list copied to clipboard!', 'success');
                }).catch(() => {
                    this.showNotification('Failed to copy epic list', 'error');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = clipboardElement.content;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    this.showNotification('Epic list copied to clipboard!', 'success');
                } catch (err) {
                    this.showNotification('Failed to copy epic list', 'error');
                }
                document.body.removeChild(textArea);
            }
        } else {
            this.showNotification('No epic list available to copy', 'warning');
        }
    },

    // Show notification
    showNotification: function(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '4px',
            color: 'white',
            fontWeight: '500',
            zIndex: '9999',
            opacity: '0',
            transform: 'translateY(-20px)',
            transition: 'all 0.3s ease-out'
        });
        
        // Set background color based on type
        const colors = {
            'success': '#52c41a',
            'error': '#ff4d4f',
            'warning': '#faad14',
            'info': '#1890ff'
        };
        notification.style.backgroundColor = colors[type] || colors.info;
        
        // Add to DOM
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    },

    // Update layout based on screen size
    updateLayout: function() {
        const container = document.querySelector('.modern-version-layout');
        if (!container) return;
        
        if (this.state.isMobile) {
            container.classList.add('mobile-layout');
            // Auto-collapse sidebar on mobile
            if (!this.state.sidebarCollapsed) {
                this.toggleSidebar();
            }
        } else {
            container.classList.remove('mobile-layout');
            // Auto-expand sidebar on desktop
            if (this.state.sidebarCollapsed) {
                this.toggleSidebar();
            }
        }
    },

    // Filter table rows (for search functionality)
    filterTable: function(searchTerm) {
        const table = document.getElementById('id-version-bugs-table');
        if (!table) return;
        
        const tbody = table.querySelector('tbody');
        const rows = tbody.querySelectorAll('tr:not(.empty-row)');
        
        if (!searchTerm) {
            rows.forEach(row => row.style.display = '');
            return;
        }
        
        const term = searchTerm.toLowerCase();
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(term) ? '' : 'none';
        });
    },

    // Get current state for debugging
    getState: function() {
        return { ...this.state };
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure all Dash components are rendered
    setTimeout(() => {
        if (document.querySelector('.modern-version-layout')) {
            window.modernVersion.init();
        }
    }, 100);
});

// Export for Dash clientside callbacks
window.dash_clientside = window.dash_clientside || {};
window.dash_clientside.modernVersion = {
    
    /**
     * Handle layer switching for Dash callback
     */
    switchLayer: function(bullet1_clicks, bullet2_clicks, bullet3_clicks, bullet5_clicks, bullet6_clicks, bullet7_clicks, bullet8_clicks) {
        const ctx = window.dash_clientside.callback_context;
        if (!ctx.triggered.length) return window.dash_clientside.no_update;
        
        const triggerId = ctx.triggered[0].prop_id.split('.')[0];
        const layerMap = {
            'id-version-bullet-1': 'one',
            'id-version-bullet-2': 'two',
            'id-version-bullet-3': 'three',
            'id-version-bullet-5': 'five',
            'id-version-bullet-6': 'six',
            'id-version-bullet-7': 'seven',
            'id-version-bullet-8': 'eight'
        };
        
        const targetLayer = layerMap[triggerId];
        if (window.modernVersion && targetLayer) {
            window.modernVersion.switchLayer(targetLayer);
        }
        
        return window.dash_clientside.no_update;
    },

    /**
     * Handle sidebar toggle
     */
    toggleSidebar: function(toggle_clicks) {
        if (window.modernVersion && toggle_clicks > 0) {
            window.modernVersion.toggleSidebar();
        }
        
        return window.dash_clientside.no_update;
    },

    /**
     * Handle table updates
     */
    updateTable: function(table_data) {
        if (window.modernVersion && table_data) {
            // Re-initialize table functionality after data update
            setTimeout(() => {
                window.modernVersion.initializeTable();
            }, 100);
        }
        
        return window.dash_clientside.no_update;
    }
};

// Global resize handler
window.addEventListener('resize', function() {
    if (window.modernVersion) {
        window.modernVersion.handleResize();
    }
});
